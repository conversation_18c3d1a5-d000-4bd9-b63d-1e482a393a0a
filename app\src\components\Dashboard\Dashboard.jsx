import { Box, Stack, Switch, Typography, Icon<PERSON><PERSON>on, <PERSON><PERSON><PERSON>, But<PERSON> } from "@mui/material";
import { useState, useCallback, useEffect } from "react";
import FilterBar from "./FilterBar";
import AllCharts from "./Graphs/AllCharts";
import ReportsDashboard from "./ReportsDashboard";
import TabbedDashboard from "./TabbedDashboard";
import DashboardKpis from "./DashboardKpis";
import SettingsIcon from "@mui/icons-material/Settings";
import ManageDashboardDialog from "./ManageDashboardDialog";
import { useDashboardCall } from "../../hooks/useDashboardCall";
import { DashboardSetting } from "@cw/rds/icons";
import useLang from '../../hooks/useLang';

const Dashboard = () => {
  const [showReports, setShowReports] = useState(false);
  const [openManageDialog, setOpenManageDialog] = useState(false);
  const [useTabbedView, setUseTabbedView] = useState(true);

  const {
    cards,
    loading,
    decisionTableConfig,
    userPreferences,
    reportConfig,
    kpiReportPrefs,
    refreshDashboard,
    sectionedCards,
    sectionedReports,
    graphLoadingStates,
    refreshSingleGraph,
    refreshMultipleGraphs
  } = useDashboardCall();

  const { t } = useLang();

  const handleDashboardUpdate = useCallback((changedKpiIds = [], changedReportIds = [], hasVisibilityChanges = false) => {
    // Refresh dashboard if there are visibility changes OR if any reports were changed
    if (hasVisibilityChanges || changedReportIds.length > 0) {
      refreshDashboard();
    }
    setOpenManageDialog(false);
  }, [refreshDashboard]);

  const handleRefreshSpecificGraphs = useCallback((kpiIds) => {
    // Refresh only the specific graphs that were changed
    refreshMultipleGraphs(kpiIds);
  }, [refreshMultipleGraphs]);
  return (
    <Box sx={{ height: "100vh", overflow: "hidden", display: "flex", flexDirection: "column" }}>
      <Box sx={{ position: "sticky", top: 0, bgcolor: "background.default", p: 2 }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h3">
              <strong>{t("Dashboard")}</strong>
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {t("This view displays various metrics related to Master Data")}
            </Typography>
          </Box>
          <Stack direction="column" spacing={1}>
            <Stack direction="row" alignItems="center" spacing={1}>
              <Button variant="outlined" startIcon={<DashboardSetting />} color="primary" onClick={() => setOpenManageDialog(true)} size="small" sx={{ mr:"20px !important"}} className="manageDashBoard" >
                {t("Manage Dashboard")}
              </Button>
              <Box sx={{display:'flex', alignItems:'center'}} className="parentChildSwitchDB">
              <Typography variant="body2">{t("KPI Metrics")}</Typography>
              <Switch checked={showReports} onChange={() => setShowReports((prev) => !prev)} color="primary"/>
              <Typography variant="body2">{t("KPI Reports")}</Typography>
              </Box>
            </Stack>
            <Stack direction="row" alignItems="center" spacing={1} justifyContent={"flex-end"} >
              <Box sx={{ display: 'flex', alignItems: 'center' }} className="tabbed">
              <Typography variant="body2">{t("Tabbed View")}</Typography>
              <Switch checked={useTabbedView} onChange={() => setUseTabbedView((prev) => !prev)} color="primary" />
              </Box>
            </Stack>
          </Stack>
        </Stack>

        {!showReports && (
          <Box mt={2}>
            {/* <FilterBar /> */}
            <DashboardKpis />
            <FilterBar />
          </Box>
        )}
      </Box>

      <Box sx={{ flex: 1, overflowY: "auto", p: 2 }}>
        {useTabbedView ? (
          <TabbedDashboard
            sectionedCards={sectionedCards}
            sectionedReports={sectionedReports}
            loading={loading}
            showReports={showReports}
            kpiReportPrefs={kpiReportPrefs}
            decisionTableConfig={decisionTableConfig}
            userPreferences={userPreferences}
            graphLoadingStates={graphLoadingStates}
            onRefreshGraph={refreshSingleGraph}
          />
        ) : (
          showReports ? (
            <ReportsDashboard
              reportConfig={reportConfig}
              kpiReportPrefs={kpiReportPrefs}
              loading={loading}
              isTabbed={false}
              userPreferences={userPreferences}
            />
          ) : (
            <AllCharts
              cards={cards}
              loading={loading}
              graphLoadingStates={graphLoadingStates}
              onRefreshGraph={refreshSingleGraph}
            />
          )
        )}
      </Box>

      <ManageDashboardDialog
        open={openManageDialog}
        onClose={() => setOpenManageDialog(false)}
        onSave={handleDashboardUpdate}
        onRefreshSpecificGraphs={handleRefreshSpecificGraphs}
        decisionTableConfig={decisionTableConfig}
        userPreferences={userPreferences}
        reportConfig={reportConfig}
      />
    </Box>
  );
};

export default Dashboard;
