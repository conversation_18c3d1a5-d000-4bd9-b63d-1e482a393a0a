import { useEffect, useState, useRef, useCallback } from "react";
import { doAjax } from "../components/Common/fetchService";
import { destination_Dashboard, destination_IDM } from "../destinationVariables";
import { useSelector } from "react-redux";
import { END_POINTS } from "@constant/apiEndPoints";
import useLogger from "@hooks/useLogger";
import { ERROR_MESSAGES, MODULE_OPTIONS } from "@constant/enum";
import {arrayToCommaString} from "@helper/helper"

const getColumnByIndex = (index) => {
  const columns = ["First", "Second", "Third"];
  return columns[index % 3];
};

const groupDataBySection = (data, type = 'metrics') => {
  const sections = {};

  data.forEach(item => {
    const sectionName = item.MDG_KPI_SECTION_NAME || 'General';
    if (!sections[sectionName]) {
      sections[sectionName] = [];
    }
    sections[sectionName].push(item);
  });

  return sections;
};

const groupGraphDataBySection = (graphData, kpiData) => {
  const sections = {};

  graphData.forEach(graph => {
    // Find the corresponding KPI data to get section name
    const kpiItem = kpiData.find(kpi => kpi.MDG_KPI_ID === graph.id || kpi.MDG_KPI_ID === `KPI_${graph.id}`);
    const sectionName = kpiItem?.MDG_KPI_SECTION_NAME || 'General';

    if (!sections[sectionName]) {
      sections[sectionName] = [];
    }
    sections[sectionName].push(graph);
  });

  // Note: Sorting is now handled by AllCharts component based on context (tabbed vs non-tabbed)

  return sections;
};

const formatApiData = (body, fallbackId, fallbackIndex, kpiId = null, graphSequence = 0) => {
  if (!body || typeof body !== "object" || Array.isArray(body)) return null;
  const rawType = body?.graphDetails?.chartType;
  const chartType = rawType?.toString()?.trim()?.toUpperCase();
  const graphName = body?.graphDetails?.graphName || "Untitled Chart";
  const colorPallete = body?.graphDetails?.color || "Pallet 1";
  const KpiSequence = body?.Sequence || graphSequence || 0;
  const id = body?.id || fallbackId;
  const column = body?.column || getColumnByIndex(id);
  if (!chartType || !graphName) return null;

  if (["PIE", "DONUT"].includes(chartType)) {
    const label = body?.label;
    const series = body?.series;
    if (!Array.isArray(label) || !Array.isArray(series)) return null;
    return {
      id,
      column,
      kpiId,
      GraphSequence: KpiSequence,
      graphDetails: { chartType, graphName, colorPallete, KpiSequence },
      label,
      series
    };
  }

  if (Array.isArray(body?.data)) {
    return {
      id,
      column,
      kpiId,
      GraphSequence: KpiSequence,
      graphDetails: { chartType, graphName, colorPallete, KpiSequence },
      data: body.data
    };
  }

  return null;
};

export const useDashboardCall = () => {
  const { customError } = useLogger();
  const [cards, setCards] = useState([]);
  const [loading, setLoading] = useState(true);
  const [decisionTableConfig, setDecisionTableConfig] = useState([]);
  const [reportConfig, setReportConfig] = useState([]);
  const [userPreferences, setUserPreferences] = useState([]);
  const [kpiPayloadMap, setKpiPayloadMap] = useState({});
  const [kpiReportPrefs, setKpiReportPrefs] = useState([]);
  const [sectionedCards, setSectionedCards] = useState({});
  const [sectionedReports, setSectionedReports] = useState({});
  const [graphLoadingStates, setGraphLoadingStates] = useState({});
  const dashboardSearchForm = useSelector((state) => state.commonFilter["Dashboard"]);

  const dashboardFilters = useSelector((state) => state.commonFilter?.Dashboard || {});
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const userData = useSelector((state) => state.userManagement.userData);
  const userId = userData?.user_id || "";
  let roles = useSelector((state) => state.userManagement.roles);
  const rolesString = roles.join(", ");

  // Helper function to validate roles
  const hasValidRoles = () => {
    return roles && Array.isArray(roles) && roles.length > 0 && roles.some(role => role && role.trim() !== '');
  };

  const hasInitializedRef = useRef(false);
  const isFetchingRef = useRef(false);
  const refreshDashboard = useCallback(async () => {
    if (isFetchingRef.current) return;
    if (!hasValidRoles()) {
      return;
    }

    isFetchingRef.current = true;
    setLoading(true);
    hasInitializedRef.current = false;

    try {
      const kpiData = await fetchDecisionTable("KPI Metrics");
      const reportData = await fetchDecisionTable("KPI Reports");
      const metricPrefs = await fetchUserPreferences(kpiData, "KPI Metrics");
      const reportPrefs = await fetchUserPreferences(reportData, "KPI Reports");
      const graphData = await fetchGraphData(kpiData, metricPrefs);

      // Group data by sections
      const sectionedKpiData = groupDataBySection(kpiData);
      const sectionedReportData = groupDataBySection(reportData);
      const sectionedGraphData = groupGraphDataBySection(graphData, kpiData);

      setKpiReportPrefs(reportPrefs);
      setCards(graphData);
      setSectionedCards(sectionedGraphData);
      setReportConfig(reportData);
      setSectionedReports(sectionedReportData);
      setUserPreferences([...metricPrefs, ...reportPrefs]);
      hasInitializedRef.current = true;
    } catch (err) {
      customError(ERROR_MESSAGES.DASHBOARD_REFRESH_FAILED, err);
    } finally {
      isFetchingRef.current = false;
      setLoading(false);
    }
  }, [roles]);



  const fetchDecisionTable = async (kpiType = "KPI Metrics") => {
    if (!hasValidRoles()) {
      return [];
    }
    const decisionPayload = {
      dtName: "MDG_MAT_DYNAMIC_DASHBOARD_DT",
      version: "v6",
      region: "US",
      role: `${rolesString}`,
      kpiType: kpiType,
    };

    let dtUrl = "";

    if (kpiType === "KPI Metrics") {
      dtUrl = `/${destination_Dashboard}${END_POINTS.DASHBOARD_APIS.FETCH_DECISION_TABLE_METRICS}`;
    } else {
      dtUrl = `/${destination_Dashboard}${END_POINTS.DASHBOARD_APIS.FETCH_DECISION_TABLE_REPORTS}`;
    }

    try {
      const result = await new Promise((resolve, reject) => {
        doAjax(
          dtUrl,
          "post",
          (res) => {
            const data = res?.body || [];
            resolve(data);
          },
          reject,
          decisionPayload
        );
      });

      if (kpiType === "KPI Metrics") {
        setDecisionTableConfig(result);
      } else {
        setReportConfig(result);
      }
      return result;
    } catch (err) {
      customError(kpiType === "KPI Metrics" ? ERROR_MESSAGES.DECISION_TABLE_FETCH_ERROR : ERROR_MESSAGES.REPORT_CONFIG_FETCH_ERROR, err);
      return [];
    }
  };

  const fetchUserPreferences = useCallback(async (dtData, kpiType) => {
    try {
      let prefs = await new Promise((resolve) => {
        doAjax(
          `/${destination_Dashboard}${END_POINTS.DASHBOARD_APIS.FETCH_USER_CONFIG}?userId=${userId}&kpiType=${encodeURIComponent(kpiType)}`,
          "get",
          (res) => resolve(res?.body || []),
          () => resolve([])
        );
      });

      if (!prefs.length && dtData.length) {
        const newPrefs = dtData.map((kpi) => ({
          Id: null,
          UserId: userId,
          KpiId: kpi.MDG_KPI_ID,
          KpiChartType: kpiType === "KPI Metrics" ? kpi.MDG_KPI_GRAPH_TYPE : "REPORT",
          KpiChartName: kpi.MDG_KPI_NAME,
          KpiColPallet: kpiType === "KPI Metrics" ? kpi.MDG_KPI_COLOR_PALLET : "",
          KpiSequence: Number(kpi.MDG_KPI_GRAPH_SEQUENCE),
          SectionSequence: 0,
          SecKpiSequence: Number(kpi.MDG_KPI_GRAPH_SEQUENCE),
          KpiColumn: kpi.MDG_KPI_GRAPH_COLUMN?.toLowerCase(),
          KpiVisibility: true,
          IsActive: true,
          KpiType: kpiType,
        }));

        await new Promise((resolve, reject) => {
          doAjax(`/${destination_Dashboard}${END_POINTS.DASHBOARD_APIS.SAVE_USER_CONFIG}`, "post", resolve, reject, newPrefs);
        });

        prefs = await new Promise((resolve) => {
          doAjax(
            `/${destination_Dashboard}${END_POINTS.DASHBOARD_APIS.FETCH_USER_CONFIG}?userId=${userId}&kpiType=${encodeURIComponent(kpiType)}`,
            "get",
            (res) => resolve(res?.body || []),
            () => resolve(newPrefs)
          );
        });
      }

      return prefs;
    } catch (err) {
      customError(ERROR_MESSAGES.USER_PREFERENCES_FETCH_ERROR, err);
      return [];
    }
  }, [userId, customError]);

  const fetchSingleGraphData = useCallback(async (kpiId, dtData, prefs) => {
    try {
      const kpi = dtData.find((d) => d.MDG_KPI_ID === kpiId);
      if (!kpi || !kpi.MDG_KPI_ENDPOINT) return null;

      const pref = prefs.find((p) => p.KpiId === kpiId);
      const endpoint = kpi.MDG_KPI_ENDPOINT.replace(/^\//, "");
      const index = parseInt(kpiId.split("_")[1]) - 1;

      const payload = {
        FromDate: "2024-01-01",
        ToDate: "2025-12-31",
        Requestor: "",
        KpiId: kpiId,
        Module: arrayToCommaString(dashboardSearchForm?.dashBoardModuleName) || MODULE_OPTIONS[0],
        UserId: userId,
        Priority: "",
        Region: dashboardFilters?.selectedRegion || "",
        ReqType: dashboardFilters?.selectedRequestType?.join(",") || "",
        ReqStatus: dashboardFilters?.selectedRequestStatus?.join(",") || "",
        GraphType: pref?.KpiChartType || kpi?.MDG_KPI_GRAPH_TYPE || "",
        KpiName: pref?.KpiChartName || kpi?.MDG_KPI_NAME || "",
        ColPallet: pref?.KpiColPallet || kpi?.MDG_KPI_COLOR_PALLET || "",
        GraphColumn: pref?.KpiColumn || kpi?.MDG_KPI_GRAPH_COLUMN?.toLowerCase() || getColumnByIndex(index),
        GraphSequence: pref?.KpiSequence || Number(kpi?.MDG_KPI_GRAPH_SEQUENCE) || index + 1,
      };

      return new Promise((resolve) => {
        doAjax(
          `/${destination_Dashboard}/counts/${endpoint}`,
          "post",
          (res) => resolve(formatApiData(res.body, index + 1, index, kpiId, payload.GraphSequence)),
          () => resolve(null),
          payload
        );
      });
    } catch (err) {
      customError(ERROR_MESSAGES.GRAPH_DATA_FETCH_ERROR, err);
      return null;
    }
  }, [dashboardSearchForm, userId, dashboardFilters, customError]);

  const fetchGraphData = async (dtData, prefs) => {
    if (!dtData.length) return [];
    try {
      const kpiEndpointMap = {};
      dtData.forEach((kpi) => {
        if (kpi.MDG_KPI_ID && kpi.MDG_KPI_ENDPOINT) {
          kpiEndpointMap[kpi.MDG_KPI_ID] = kpi.MDG_KPI_ENDPOINT.replace(/^\//, "");
        }
      });
      const enabledIds_DT = dtData.filter((d) => ["TRUE", "ENABLED"].includes((d.MDG_KPI_VISIBILITY || "").toString().toUpperCase())).map((d) => d.MDG_KPI_ID);

      const enabledIds_User = prefs.filter((p) => p.KpiVisibility === true && p.IsActive === true).map((p) => p.KpiId);

      const enabledKpiIds = enabledIds_User.length > 0 ? enabledIds_DT.filter((id) => enabledIds_User.includes(id)) : enabledIds_DT;
      const payloadMap = {};
      const results = await Promise.all(
        enabledKpiIds.map((kpiId) => {
          const pref = prefs.find((p) => p.KpiId === kpiId);
          const dt = dtData.find((p) => p.MDG_KPI_ID === kpiId);
          const endpoint = kpiEndpointMap[kpiId];
          if (!endpoint) return Promise.resolve(null);

          const index = parseInt(kpiId.split("_")[1]) - 1;
          const payload = {
            FromDate: "2024-01-01",
            ToDate: "2025-12-31",
            Requestor: "",
            KpiId: kpiId,
            Module: arrayToCommaString(dashboardSearchForm?.dashBoardModuleName) || MODULE_OPTIONS[0],
            UserId: userId,
            Priority: "",
            Region: dashboardFilters?.selectedRegion || "",
            ReqType: dashboardFilters?.selectedRequestType?.join(",") || "",
            ReqStatus: dashboardFilters?.selectedRequestStatus?.join(",") || "",
            GraphType: pref?.KpiChartType || dt?.MDG_KPI_GRAPH_TYPE || "",
            KpiName: pref?.KpiChartName || dt?.MDG_KPI_NAME || "",
            ColPallet: pref?.KpiColPallet || dt?.MDG_KPI_COLOR_PALLET || "",
            GraphColumn: pref?.KpiColumn || dt?.MDG_KPI_GRAPH_COLUMN?.toLowerCase() || getColumnByIndex(index),
            GraphSequence: pref?.KpiSequence || Number(dt?.MDG_KPI_GRAPH_SEQUENCE) || index + 1,
          };

          payloadMap[kpiId] = payload;

          return new Promise((resolve) => {
            doAjax(
              `/${destination_Dashboard}/counts/${endpoint}`,
              "post",
              (res) => resolve(formatApiData(res.body, index + 1, index, kpiId, payload.GraphSequence)),
              () => resolve(null),
              payload
            );
          });
        })
      );

      setKpiPayloadMap(payloadMap);
      return results.filter(Boolean);
    } catch (err) {
      customError(ERROR_MESSAGES.GRAPH_DATA_FETCH_ERROR, err);
      return [];
    }
  };

  const initializeDashboard = async () => {
    if (isFetchingRef.current || hasInitializedRef.current) return;
    if (!hasValidRoles()) {
      return;
    }

    isFetchingRef.current = true;
    setLoading(true);
    try {
      const kpiData = await fetchDecisionTable("KPI Metrics");
      const reportData = await fetchDecisionTable("KPI Reports");
      const metricPrefs = await fetchUserPreferences(kpiData, "KPI Metrics");
      const reportPrefs = await fetchUserPreferences(reportData, "KPI Reports");
      const graphData = await fetchGraphData(kpiData, metricPrefs);

      // Group data by sections
      const sectionedReportData = groupDataBySection(reportData);
      const sectionedGraphData = groupGraphDataBySection(graphData, kpiData);

      setCards(graphData);
      setSectionedCards(sectionedGraphData);
      setKpiReportPrefs(reportPrefs);
      setReportConfig(reportData);
      setSectionedReports(sectionedReportData);
      setUserPreferences([...metricPrefs, ...reportPrefs]);
      hasInitializedRef.current = true;
    } catch (err) {
      customError(ERROR_MESSAGES.DASHBOARD_INIT_FAILED, err);
    } finally {
      isFetchingRef.current = false;
      setLoading(false);
    }
  };

  useEffect(() => {
    if (userId || userId !== "") {
      initializeDashboard();
    }
  }, [userId]);

  // Watch for roles and initialize dashboard when roles become available
  useEffect(() => {
    if (hasValidRoles() && userId && !hasInitializedRef.current && !isFetchingRef.current) {
      initializeDashboard();
    }
  }, [roles, userId]);

  useEffect(() => {
    const updateOnFilters = async () => {
      if (!hasInitializedRef.current || isFetchingRef.current) return;

      isFetchingRef.current = true;
      setLoading(true);
      try {
        const graphData = await fetchGraphData(decisionTableConfig, userPreferences);
        const sectionedGraphData = groupGraphDataBySection(graphData, decisionTableConfig);
        setCards(graphData);
        setSectionedCards(sectionedGraphData);
      } catch (err) {
        customError(ERROR_MESSAGES.FILTER_CHANGE_UPDATE_FAILED, err);
      } finally {
        isFetchingRef.current = false;
        setLoading(false);
      }
    };
    updateOnFilters();
  }, [dashboardFilters]);

  const refreshSingleGraph = useCallback(async (kpiId) => {
    if (!decisionTableConfig.length || !userPreferences.length) return;

    // Check if this graph should be visible based on user preferences
    const userPref = userPreferences.find(p => p.KpiId === kpiId);
    const dtConfig = decisionTableConfig.find(d => d.MDG_KPI_ID === kpiId);

    const isDTEnabled = dtConfig && ["TRUE", "ENABLED"].includes((dtConfig.MDG_KPI_VISIBILITY || "").toString().toUpperCase());
    const isUserEnabled = userPref && userPref.KpiVisibility === true && userPref.IsActive === true;

    if (!isDTEnabled || !isUserEnabled) {
      // Remove the graph if it's disabled
      setCards(prevCards => prevCards.filter(card => card.kpiId !== kpiId));
      setSectionedCards(prevSectioned => {
        const updatedSectioned = { ...prevSectioned };
        Object.keys(updatedSectioned).forEach(sectionName => {
          updatedSectioned[sectionName] = updatedSectioned[sectionName]
            .filter(card => card.kpiId !== kpiId);
        });
        return updatedSectioned;
      });
      return;
    }

    // Set loading state for this specific graph
    setGraphLoadingStates(prev => ({ ...prev, [kpiId]: true }));

    try {
      const newGraphData = await fetchSingleGraphData(kpiId, decisionTableConfig, userPreferences);

      if (newGraphData) {
        // Update the specific graph in cards array
        setCards(prevCards => {
          const existingIndex = prevCards.findIndex(card => card.kpiId === kpiId);
          if (existingIndex >= 0) {
            const updatedCards = [...prevCards];
            updatedCards[existingIndex] = newGraphData;
            return updatedCards;
          } else {
            return [...prevCards, newGraphData];
          }
        });

        // Update sectioned cards
        setSectionedCards(prevSectioned => {
          const kpiItem = decisionTableConfig.find(kpi => kpi.MDG_KPI_ID === kpiId);
          const sectionName = kpiItem?.MDG_KPI_SECTION_NAME || 'General';

          const updatedSectioned = { ...prevSectioned };
          if (!updatedSectioned[sectionName]) {
            updatedSectioned[sectionName] = [];
          }

          const existingIndex = updatedSectioned[sectionName].findIndex(card => card.kpiId === kpiId);
          if (existingIndex >= 0) {
            updatedSectioned[sectionName][existingIndex] = newGraphData;
          } else {
            updatedSectioned[sectionName].push(newGraphData);
          }

          return updatedSectioned;
        });
      }
    } catch (err) {
      customError(ERROR_MESSAGES.GRAPH_DATA_FETCH_ERROR, err);
    } finally {
      // Clear loading state for this specific graph
      setGraphLoadingStates(prev => ({ ...prev, [kpiId]: false }));
    }
  }, [decisionTableConfig, userPreferences, fetchSingleGraphData, customError]);

  const refreshMultipleGraphs = useCallback(async (kpiIds) => {
    if (!decisionTableConfig.length || !kpiIds.length) return;

    try {
      const updatedPrefs = await fetchUserPreferences(decisionTableConfig, "KPI Metrics");
      setUserPreferences(prev => {
        const reportPrefs = prev.filter(p => p.KpiType === "KPI Reports");
        return [...updatedPrefs, ...reportPrefs];
      });

      const enabledIds_DT = decisionTableConfig
        .filter((d) => ["TRUE", "ENABLED"].includes((d.MDG_KPI_VISIBILITY || "").toString().toUpperCase()))
        .map((d) => d.MDG_KPI_ID);

      const enabledIds_User = updatedPrefs
        .filter((p) => p.KpiVisibility === true && p.IsActive === true)
        .map((p) => p.KpiId);

      const currentlyEnabledKpis = enabledIds_User.length > 0
        ? enabledIds_DT.filter((id) => enabledIds_User.includes(id))
        : enabledIds_DT;

      // Separate KPIs into those that need data fetching vs those that need removal
      const kpisToFetch = kpiIds.filter(kpiId => currentlyEnabledKpis.includes(kpiId));
      const kpisToRemove = kpiIds.filter(kpiId => !currentlyEnabledKpis.includes(kpiId));

      // Set loading states for graphs being fetched
      const loadingStates = {};
      kpisToFetch.forEach(kpiId => {
        loadingStates[kpiId] = true;
      });
      setGraphLoadingStates(prev => ({ ...prev, ...loadingStates }));

      // Remove disabled graphs from display
      if (kpisToRemove.length > 0) {
        setCards(prevCards => prevCards.filter(card => !kpisToRemove.includes(card.kpiId)));

        setSectionedCards(prevSectioned => {
          const updatedSectioned = { ...prevSectioned };
          Object.keys(updatedSectioned).forEach(sectionName => {
            updatedSectioned[sectionName] = updatedSectioned[sectionName]
              .filter(card => !kpisToRemove.includes(card.kpiId));
          });
          return updatedSectioned;
        });
      }

      // Fetch data for enabled graphs
      if (kpisToFetch.length > 0) {
        const graphDataPromises = kpisToFetch.map(kpiId =>
          fetchSingleGraphData(kpiId, decisionTableConfig, updatedPrefs)
        );

        const graphDataResults = await Promise.all(graphDataPromises);
        const validGraphData = graphDataResults.filter(Boolean);

        if (validGraphData.length > 0) {
          // Update cards array
          setCards(prevCards => {
            let updatedCards = [...prevCards];

            validGraphData.forEach(newGraphData => {
              const existingIndex = updatedCards.findIndex(card => card.kpiId === newGraphData.kpiId);
              if (existingIndex >= 0) {
                updatedCards[existingIndex] = newGraphData;
              } else {
                updatedCards.push(newGraphData);
              }
            });

            return updatedCards;
          });

          // Update sectioned cards
          setSectionedCards(prevSectioned => {
            const updatedSectioned = { ...prevSectioned };

            validGraphData.forEach(newGraphData => {
              const kpiItem = decisionTableConfig.find(kpi => kpi.MDG_KPI_ID === newGraphData.kpiId);
              const sectionName = kpiItem?.MDG_KPI_SECTION_NAME || 'General';

              if (!updatedSectioned[sectionName]) {
                updatedSectioned[sectionName] = [];
              }

              const existingIndex = updatedSectioned[sectionName].findIndex(card => card.kpiId === newGraphData.kpiId);
              if (existingIndex >= 0) {
                updatedSectioned[sectionName][existingIndex] = newGraphData;
              } else {
                updatedSectioned[sectionName].push(newGraphData);
              }
            });

            return updatedSectioned;
          });
        }
      }
    } catch (err) {
      customError(ERROR_MESSAGES.GRAPH_DATA_FETCH_ERROR, err);
    } finally {
      const clearedStates = {};
      kpiIds.forEach(kpiId => {
        clearedStates[kpiId] = false;
      });
      setGraphLoadingStates(prev => ({ ...prev, ...clearedStates }));
    }
  }, [decisionTableConfig, fetchSingleGraphData, fetchUserPreferences, customError]);

  return {
    cards,
    reportConfig,
    loading,
    decisionTableConfig,
    userPreferences,
    kpiPayloadMap,
    kpiReportPrefs,
    refreshDashboard,
    sectionedCards,
    sectionedReports,
    graphLoadingStates,
    refreshSingleGraph,
    refreshMultipleGraphs,
  };
};
