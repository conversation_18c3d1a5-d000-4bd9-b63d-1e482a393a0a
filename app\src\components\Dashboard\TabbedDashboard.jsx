import React, { useState } from 'react';
import { Box, Tabs, Tab, Typography, CircularProgress } from '@mui/material';
import AllCharts from './Graphs/AllCharts';
import ReportsDashboard from './ReportsDashboard';
import useLang from '../../hooks/useLang';
import { ERROR_MESSAGES } from '../../constant/enum';

const TabPanel = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`dashboard-tabpanel-${index}`}
      aria-labelledby={`dashboard-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};



const TabbedDashboard = ({
  sectionedCards = {},
  sectionedReports = {},
  loading = false,
  reportsLoading = false,
  showReports = false,
  kpiReportPrefs = [],
  graphLoadingStates = {},
  onRefreshGraph = () => {},
  decisionTableConfig = [],
  userPreferences = []
}) => {
  const { t } = useLang();
  const [activeTab, setActiveTab] = useState(0);

  // Get section names and sort them by section sequence
  const getSortedSections = () => {
    const sectionData = showReports ? sectionedReports : sectionedCards;
    const sectionNames = Object.keys(sectionData);

    // Sort sections by their sequence from user preferences
    return sectionNames.sort((a, b) => {
      // Find section sequence from user preferences
      const sectionAItems = userPreferences.filter(pref => {
        // Find the corresponding decision table item to get section name
        const dtItem = decisionTableConfig.find(dt => dt.MDG_KPI_ID === pref.KpiId);
        const sectionName = dtItem?.MDG_KPI_SECTION_NAME || 'General';
        return sectionName === a;
      });

      const sectionBItems = userPreferences.filter(pref => {
        // Find the corresponding decision table item to get section name
        const dtItem = decisionTableConfig.find(dt => dt.MDG_KPI_ID === pref.KpiId);
        const sectionName = dtItem?.MDG_KPI_SECTION_NAME || 'General';
        return sectionName === b;
      });

      // Get the minimum section sequence for each section
      const sectionASequence = sectionAItems.length > 0
        ? Math.min(...sectionAItems.map(pref => pref.SectionSequence || 0))
        : 0;
      const sectionBSequence = sectionBItems.length > 0
        ? Math.min(...sectionBItems.map(pref => pref.SectionSequence || 0))
        : 0;

      return sectionASequence - sectionBSequence;
    });
  };

  const sections = getSortedSections();
  
  // If no sections, show default view
  if (sections.length === 0) {
    if (loading) {
      return (
        <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100%" }}>
          <CircularProgress />
        </Box>
      );
    }
    
    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="h6" color="textSecondary">
          {t(ERROR_MESSAGES.NO_DATA_AVAILABLE)}
        </Typography>
      </Box>
    );
  }

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const a11yProps = (index) => {
    return {
      id: `dashboard-tab-${index}`,
      'aria-controls': `dashboard-tabpanel-${index}`,
    };
  };

  if (loading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100%" }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs 
          value={activeTab} 
          onChange={handleTabChange} 
          aria-label="dashboard sections"
          variant="scrollable"
          scrollButtons="auto"
        >
          {sections.map((sectionName, index) => (
            <Tab 
              key={sectionName}
              label={t(sectionName)} 
              {...a11yProps(index)} 
            />
          ))}
        </Tabs>
      </Box>

      {sections.map((sectionName, index) => (
        <TabPanel key={sectionName} value={activeTab} index={index}>
          {showReports ? (
            <ReportsDashboard
              key={`reports-${sectionName}`}
              reportConfig={sectionedReports[sectionName] || []}
              kpiReportPrefs={kpiReportPrefs}
              loading={reportsLoading}
              isTabbed={true}
              userPreferences={userPreferences}
            />
          ) : (
            <AllCharts
              key={`charts-${sectionName}`}
              cards={sectionedCards[sectionName] || []}
              loading={false}
              graphLoadingStates={graphLoadingStates}
              onRefreshGraph={onRefreshGraph}
              isTabbed={true}
              userPreferences={userPreferences}
            />
          )}
        </TabPanel>
      ))}
    </Box>
  );
};

export default TabbedDashboard;
